import fs from 'fs';
import { drive_v3, google } from 'googleapis';
import logger from './logger';

// Helper function to find a file by name in a specific folder
async function findFileByName(
  drive: drive_v3.Drive,
  fileName: string,
  folderId: string
): Promise<drive_v3.Schema$File | null> {
  try {
    const cleanFileName = fileName.replace(/'/g, "\\\\'");
    const res = await drive.files.list({
      q: `name='${cleanFileName}' and '${folderId}' in parents and trashed=false and mimeType!='application/vnd.google-apps.folder'`,
      fields: 'files(id, name)',
      spaces: 'drive',
      pageSize: 1,
    });
    if (res.data.files && res.data.files.length > 0) {
      return res.data.files[0];
    }
    return null;
  } catch (error: unknown) {
    const message = error instanceof Error ? error.message : String(error);
    logger.error(
      `[gdrive] Error searching for file "${fileName}" in folder "${folderId}": ${message}`
    );
    if (error instanceof Error) throw error;
    throw new Error(`Error searching for file: ${message}`);
  }
}

export async function uploadToGoogleDrive(
  filePath: string,
  fileName: string,
  folderId: string,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  auth: any
): Promise<string | null | undefined> {
  const drive = google.drive({ version: 'v3', auth });

  logger.info(`[gdrive] Attempting to upload ${fileName} to Google Drive folder ID: ${folderId}`);

  try {
    const existingFile = await findFileByName(drive, fileName, folderId);
    const media = { body: fs.createReadStream(filePath) };

    if (existingFile && existingFile.id) {
      logger.info(
        `[gdrive] File "${fileName}" already exists with ID "${existingFile.id}". Updating it.`
      );
      const res = await drive.files.update({
        fileId: existingFile.id,
        media: media,
        fields: 'id, webViewLink',
      });
      logger.info(`[gdrive] File updated successfully. View link: ${res.data.webViewLink}`);
      return res.data.webViewLink;
    } else {
      logger.info(`[gdrive] File "${fileName}" not found. Creating new file.`);
      const fileMetadata = { name: fileName, parents: [folderId] };
      const res = await drive.files.create({
        requestBody: fileMetadata,
        media: media,
        fields: 'id, webViewLink',
      });
      logger.info(`[gdrive] File uploaded successfully. View link: ${res.data.webViewLink}`);
      return res.data.webViewLink;
    }
  } catch (error: unknown) {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const GaxiosError = (error as any)?.errors;

    let errorMessage = 'Unknown error during Google Drive upload';
    if (error instanceof Error) {
      errorMessage = error.message;
    } else if (typeof error === 'string') {
      errorMessage = error;
    }

    logger.error(`[gdrive] Error uploading file ${fileName}: ${errorMessage}`, {
      filePath,
      fileName,
      folderId,
    });

    if (GaxiosError && Array.isArray(GaxiosError)) {
      GaxiosError.forEach((err: { domain?: string; reason?: string; message?: string }) => {
        logger.error(
          `[gdrive] Google API Error: domain=${err.domain || 'N/A'}, reason=${err.reason || 'N/A'}, message=${err.message || 'N/A'}`
        );
      });
    }
    return null;
  }
}
