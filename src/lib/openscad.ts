import { execFile } from 'child_process';
import fse from 'fs-extra';
import path from 'path';
import { getLogger } from './shared/logging';
import { RenderSettings } from './types';

const logger = getLogger('openscad');

export async function generateStl(
  openscadPath: string,
  modelPath: string,
  parameters: Record<string, string | number | boolean>,
  outputStlPath: string,
  renderSettings: RenderSettings,
  tempDir: string
): Promise<string> {
  const tempScadPath = path.join(tempDir, `temp_${path.basename(modelPath)}`);
  let scadContent = await fse.readFile(modelPath, 'utf8');

  for (const key in parameters) {
    const regex = new RegExp(`\\\\$${key}\\\\b`, 'g');
    scadContent = scadContent.replace(regex, String(parameters[key]));
  }

  // Add OpenSCAD quality settings with defaults
  const fa = renderSettings.fa ?? 12;
  const fs = renderSettings.fs ?? 2;
  const fn = renderSettings.fn ?? 0;
  scadContent += `\\n$fa = ${fa};\\n$fs = ${fs};\\n$fn = ${fn};\\n`;

  await fse.writeFile(tempScadPath, scadContent);
  await fse.ensureDir(path.dirname(outputStlPath));

  if (await fse.pathExists(outputStlPath)) {
    logger.info(
      `[openscad] Existing STL file found at ${outputStlPath}. Deleting before regeneration.`
    );
    await fse.remove(outputStlPath);
  }

  const openscadCommand = openscadPath || 'openscad';

  logger.info(
    `[openscad] Rendering STL: ${openscadCommand} -o "${outputStlPath}" "${tempScadPath}"`
  );

  try {
    await new Promise<void>((resolve, reject) => {
      execFile(openscadCommand, ['-o', outputStlPath, tempScadPath], (error, stdout, stderr) => {
        if (error) {
          logger.error(`[openscad] Failed to generate STL: ${error.message}`);
          logger.error(`[openscad] stderr: ${stderr}`);
          if (stdout) logger.error(`[openscad] stdout: ${stdout}`);
          return reject(error);
        }
        if (stderr) {
          logger.warn(`[openscad] Render warnings/stderr: ${stderr}`);
        }
        logger.info(`[openscad] STL file generated successfully: ${outputStlPath}`);
        logger.debug(`[openscad] Render stdout: ${stdout}`);
        resolve();
      });
    });
  } catch (error: unknown) {
    if (error instanceof Error) {
      throw new Error(`OpenSCAD rendering failed: ${error.message}`);
    }
    throw new Error(`OpenSCAD rendering failed with an unknown error.`);
  } finally {
    try {
      await fse.remove(tempScadPath);
      logger.debug(`[openscad] Cleaned up temporary file: ${tempScadPath}`);
    } catch (cleanupError: unknown) {
      const message = cleanupError instanceof Error ? cleanupError.message : String(cleanupError);
      logger.warn(`[openscad] Could not clean up temporary file ${tempScadPath}: ${message}`);
    }
  }

  return outputStlPath;
}
