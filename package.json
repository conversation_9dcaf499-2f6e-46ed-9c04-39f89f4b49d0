{"name": "y3dhub", "version": "0.1.0", "private": true, "type": "module", "scripts": {"prepare": "husky", "lint-staged": "lint-staged", "dev": "next dev -p 3002 -H 0.0.0.0", "build": "SKIP_DATABASE_VALIDATION=true NEXT_TELEMETRY_DISABLED=1 next build --no-lint", "start": "next start -p 3001 -H 0.0.0.0", "start-prod-http-3000": "next start -p 3000 -H 0.0.0.0", "lint": "next lint", "lint:full": "next lint --dir src/", "lint:fix": "next lint --fix", "lint:ci": "node scripts/ci/lint-with-cache.js", "lint:md:fix": "npx markdownlint --fix .", "db:generate": "prisma generate", "db:migrate": "prisma migrate dev", "db:seed": "node --require dotenv/config --import tsx prisma/seed.ts", "db:studio": "prisma studio", "db:reset": "prisma migrate reset --force", "db:test:setup": "bash scripts/setup-sqlite-db.sh", "dev:offline": "cp .env.offline .env && npm run db:offline:setup && npm run dev", "build:offline": "cp .env.offline .env && npm run db:offline:setup && npm run build", "db:offline:setup": "bash scripts/setup-offline-dev.sh", "postinstall": "prisma generate", "populate-queue": "tsx src/scripts/populate-print-queue.ts", "sync-orders": "node --require dotenv/config --import tsx src/scripts/sync-orders.ts", "clean": "tsx src/scripts/clean.ts", "clean:all": "tsx src/scripts/clean.ts --browser-caches --node-modules", "clean:dry": "tsx src/scripts/clean.ts --dry-run --verbose", "cleanup-print-tasks": "tsx scripts/cleanup-print-tasks.ts", "check:amazon-urls": "tsx scripts/check-amazon-urls.ts", "check:sync-status": "tsx scripts/check-shipstation-sync-status.ts", "full-workflow": "bash scripts/workflow.sh", "seed:test-user": "tsx src/scripts/seed-test-user.ts", "test:coverage": "vitest run --coverage", "test:e2e": "echo 'Skipping E2E tests' && exit 0", "test:local": "bash scripts/setup-sqlite-db.sh && DATABASE_URL=file:./test.db vitest run", "docs:gen-commands": "tsx scripts/docs/gen-commands-table.ts", "test": "vitest run", "worker:stl": "tsx src/workers/stl-render-worker.ts", "worker:stlrefresh": "tsx src/workers/stl-render-worker.ts --refresh", "worker:stl-clear-gdrive": "tsx src/workers/stl-render-worker.ts --clear-gdrive", "worker:ai-monitor": "tsx src/scripts/ai-processor.ts", "type-check": "tsc --noEmit", "git:reset-staging": "bash scripts/git/reset-to-staging.sh", "git:merge-to-main": "bash scripts/git/merge-staging-to-main.sh", "email:test": "tsx src/scripts/sendgrid-test.ts", "find-orders:no-pending-tasks": "npx tsx scripts/find-pending-orders-no-print-tasks.ts"}, "dependencies": {"@heroicons/react": "^2.2.0", "@next-auth/prisma-adapter": "^1.0.7", "@nextui-org/react": "^2.6.11", "@prisma/client": "^6.6.0", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-aspect-ratio": "^1.0.3", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-context-menu": "^2.2.6", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-hover-card": "^1.1.6", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-menubar": "^1.1.6", "@radix-ui/react-navigation-menu": "^1.2.5", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toggle": "^1.1.2", "@radix-ui/react-toggle-group": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.8", "@sendgrid/mail": "^8.1.5", "@tanstack/react-table": "^8.19.3", "@types/bwip-js": "^3.2.3", "@types/fs-extra": "^11.0.4", "@types/lodash.debounce": "^4.0.9", "axios": "^1.8.4", "bcryptjs": "^3.0.2", "bwip-js": "^4.6.0", "chart.js": "^4.4.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "commander": "^13.1.0", "date-fns": "^3.6.0", "date-fns-tz": "^3.2.0", "dotenv": "^16.5.0", "framer-motion": "^12.7.3", "fs-extra": "^11.3.0", "googleapis": "^148.0.0", "jszip": "^3.10.1", "lodash.debounce": "^4.0.8", "lucide-react": "^0.485.0", "next-auth": "^4.24.11", "next-themes": "^0.3.0", "openai": "^4.94.0", "papaparse": "^5.4.1", "pino": "^9.6.0", "react-chartjs-2": "^5.3.0", "react-day-picker": "^8.10.1", "react-hook-form": "^7.55.0", "react-hot-toast": "^2.5.2", "react-resizable-panels": "^3.0.2", "react18-json-view": "^0.2.9", "sonner": "^2.0.3", "tailwind-merge": "^3.0.2", "tw-animate-css": "^1.2.5", "vaul": "^1.1.2", "winston": "^3.17.0", "yargs": "^17.7.2", "zod": "^3.24.2"}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@eslint/eslintrc": "^3.3.1", "@tailwindcss/postcss": "^4", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/jest": "^29.5.14", "@types/minimist": "^1.2.5", "@types/node": "^20", "@types/papaparse": "^5.3.14", "@types/react": "^18", "@types/react-dom": "^18", "@types/slug": "^5.0.0", "@types/yargs": "^17.0.33", "@typescript-eslint/eslint-plugin": "^8.32.1", "@typescript-eslint/parser": "^8.32.1", "eslint": "^8.57.1", "eslint-config-next": "^14.2.5", "eslint-import-resolver-typescript": "^4.3.2", "eslint-plugin-import": "^2.31.0", "eslint-plugin-react-hooks": "^5.2.0", "globby": "^14.1.0", "husky": "^9.1.7", "jsdom": "^26.1.0", "lint-staged": "^16.0.0", "markdownlint-cli": "^0.44.0", "next": "^14.2.5", "postcss": "^8.5.3", "prettier": "^3.5.3", "prisma": "^6.6.0", "react": "^18.3.1", "react-dom": "^18.3.1", "slug": "^10.0.0", "tsx": "^4.19.4", "typescript": "^5.8.3", "vitest": "^3.1.3"}}