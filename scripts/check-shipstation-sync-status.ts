import { PrismaClient } from '@prisma/client';
import { Command } from 'commander';
import { formatISO, subDays } from 'date-fns';
import { fetchOrdersWithRetry } from '../src/lib/shipstation/api';
import { ShipStationApiParams } from '../src/lib/shipstation/types';

const prisma = new PrismaClient();

interface Options {
  days?: number;
  status?: string;
  dryRun?: boolean;
  verbose?: boolean;
}

interface SyncCheckResult {
  orderNumber: string;
  shipstationOrderId: string;
  status: string;
  inDatabase: boolean;
  dbOrderId?: number;
  shipstationItemCount: number;
  dbItemCount: number;
  printTaskCount: number;
  hasAllTasks: boolean;
  syncStatus: 'SYNCED' | 'MISSING_FROM_DB' | 'ITEM_MISMATCH' | 'NO_PRINT_TASKS' | 'PARTIAL_TASKS';
  issues: string[];
}

/**
 * Fetch all outstanding orders from ShipStation API
 */
async function fetchShipStationOrders(options: Options): Promise<Map<string, any>> {
  console.log(`\n🔍 Fetching orders from ShipStation API...`);
  
  const shipstationOrders = new Map<string, any>();
  let currentPage = 1;
  let totalPages = 1;
  let totalOrdersChecked = 0;

  const apiParams: ShipStationApiParams = {
    orderStatus: options.status || 'awaiting_shipment',
    sortBy: 'CreateDate',
    sortDir: 'DESC',
    pageSize: 100,
  };

  if (options.days && options.days > 0) {
    apiParams.createDateStart = formatISO(subDays(new Date(), options.days));
    console.log(`   - Filtering orders created since: ${apiParams.createDateStart}`);
  }

  try {
    do {
      apiParams.page = currentPage;
      const response = await fetchOrdersWithRetry(apiParams);

      if (!response || !response.orders) {
        console.warn(`No orders found on page ${currentPage} or failed fetch.`);
        break;
      }

      totalPages = response.pages;
      console.log(`   Page ${currentPage}/${totalPages}: ${response.orders.length} orders`);

      for (const order of response.orders) {
        shipstationOrders.set(order.orderNumber, {
          orderId: order.orderId,
          orderNumber: order.orderNumber,
          status: order.orderStatus,
          itemCount: order.items.length,
          createDate: order.createDate,
          modifyDate: order.modifyDate,
        });
        totalOrdersChecked++;
      }

      currentPage++;
    } while (currentPage <= totalPages);

  } catch (error) {
    console.error('Error fetching orders from ShipStation:', error);
    throw error;
  }

  console.log(`   ✅ Fetched ${totalOrdersChecked} orders from ShipStation`);
  return shipstationOrders;
}

/**
 * Check database for corresponding orders and their print tasks
 */
async function checkDatabaseSync(shipstationOrders: Map<string, any>): Promise<SyncCheckResult[]> {
  console.log(`\n🔍 Checking database sync status...`);
  
  const results: SyncCheckResult[] = [];
  const orderNumbers = Array.from(shipstationOrders.keys());

  // Fetch all corresponding orders from database in one query
  const dbOrders = await prisma.order.findMany({
    where: {
      shipstation_order_number: {
        in: orderNumbers,
      },
    },
    include: {
      OrderItem: {
        include: {
          PrintOrderTask: true,
        },
      },
      PrintOrderTask: true,
    },
  });

  // Create a map for quick lookup
  const dbOrderMap = new Map(
    dbOrders.map(order => [order.shipstation_order_number!, order])
  );

  // Check each ShipStation order
  for (const [orderNumber, ssOrder] of shipstationOrders) {
    const dbOrder = dbOrderMap.get(orderNumber);
    const issues: string[] = [];
    
    let syncStatus: SyncCheckResult['syncStatus'] = 'SYNCED';
    let hasAllTasks = false;
    let dbItemCount = 0;
    let printTaskCount = 0;

    if (!dbOrder) {
      syncStatus = 'MISSING_FROM_DB';
      issues.push('Order not found in database');
    } else {
      dbItemCount = dbOrder.OrderItem.length;
      printTaskCount = dbOrder.PrintOrderTask.length;

      // Check item count mismatch
      if (dbItemCount !== ssOrder.itemCount) {
        syncStatus = 'ITEM_MISMATCH';
        issues.push(`Item count mismatch: SS=${ssOrder.itemCount}, DB=${dbItemCount}`);
      }

      // Check print tasks
      if (printTaskCount === 0) {
        syncStatus = 'NO_PRINT_TASKS';
        issues.push('No print tasks found');
      } else {
        // Check if we have at least one task per item
        const expectedMinTasks = Math.max(1, dbItemCount); // At least 1 task, or 1 per item
        if (printTaskCount < expectedMinTasks) {
          syncStatus = 'PARTIAL_TASKS';
          issues.push(`Insufficient print tasks: expected ≥${expectedMinTasks}, found ${printTaskCount}`);
        } else {
          hasAllTasks = true;
        }
      }

      // If no other issues, mark as synced
      if (issues.length === 0) {
        syncStatus = 'SYNCED';
      }
    }

    results.push({
      orderNumber,
      shipstationOrderId: ssOrder.orderId.toString(),
      status: ssOrder.status,
      inDatabase: !!dbOrder,
      dbOrderId: dbOrder?.id,
      shipstationItemCount: ssOrder.itemCount,
      dbItemCount,
      printTaskCount,
      hasAllTasks,
      syncStatus,
      issues,
    });
  }

  return results;
}

/**
 * Display results and summary
 */
function displayResults(results: SyncCheckResult[], options: Options) {
  console.log(`\n📊 SYNC STATUS REPORT`);
  console.log(`${'='.repeat(80)}`);

  // Group results by sync status
  const grouped = results.reduce((acc, result) => {
    if (!acc[result.syncStatus]) acc[result.syncStatus] = [];
    acc[result.syncStatus].push(result);
    return acc;
  }, {} as Record<string, SyncCheckResult[]>);

  // Display summary
  console.log(`\n📈 SUMMARY:`);
  console.log(`   Total Orders Checked: ${results.length}`);
  console.log(`   ✅ Fully Synced: ${grouped.SYNCED?.length || 0}`);
  console.log(`   ❌ Missing from DB: ${grouped.MISSING_FROM_DB?.length || 0}`);
  console.log(`   ⚠️  Item Mismatch: ${grouped.ITEM_MISMATCH?.length || 0}`);
  console.log(`   🚫 No Print Tasks: ${grouped.NO_PRINT_TASKS?.length || 0}`);
  console.log(`   ⚡ Partial Tasks: ${grouped.PARTIAL_TASKS?.length || 0}`);

  // Display detailed issues if any
  const problemOrders = results.filter(r => r.syncStatus !== 'SYNCED');
  
  if (problemOrders.length > 0) {
    console.log(`\n🚨 ORDERS WITH ISSUES (${problemOrders.length}):`);
    console.log(`${'='.repeat(80)}`);
    
    for (const result of problemOrders) {
      console.log(`\n📋 Order: ${result.orderNumber} (SS ID: ${result.shipstationOrderId})`);
      console.log(`   Status: ${result.status}`);
      console.log(`   Sync Status: ${result.syncStatus}`);
      console.log(`   In Database: ${result.inDatabase ? 'Yes' : 'No'}`);
      if (result.inDatabase) {
        console.log(`   DB Order ID: ${result.dbOrderId}`);
        console.log(`   Items: SS=${result.shipstationItemCount}, DB=${result.dbItemCount}`);
        console.log(`   Print Tasks: ${result.printTaskCount}`);
      }
      if (result.issues.length > 0) {
        console.log(`   Issues:`);
        result.issues.forEach(issue => console.log(`     - ${issue}`));
      }
    }

    // Provide recommendations
    console.log(`\n💡 RECOMMENDATIONS:`);
    
    if (grouped.MISSING_FROM_DB?.length > 0) {
      console.log(`\n🔄 Orders Missing from Database (${grouped.MISSING_FROM_DB.length}):`);
      console.log(`   Run sync to import these orders:`);
      console.log(`   npm run sync-orders -- --mode recent --days-back 7`);
    }

    if (grouped.NO_PRINT_TASKS?.length > 0 || grouped.PARTIAL_TASKS?.length > 0) {
      const needTasksCount = (grouped.NO_PRINT_TASKS?.length || 0) + (grouped.PARTIAL_TASKS?.length || 0);
      console.log(`\n📝 Orders Needing Print Tasks (${needTasksCount}):`);
      console.log(`   Run populate print queue for these orders:`);
      console.log(`   npm run populate-queue -- --limit 50`);
    }

    if (grouped.ITEM_MISMATCH?.length > 0) {
      console.log(`\n⚠️  Orders with Item Mismatches (${grouped.ITEM_MISMATCH.length}):`);
      console.log(`   These may need manual review or re-sync`);
    }
  } else {
    console.log(`\n🎉 All orders are properly synced!`);
  }
}

/**
 * Main execution function
 */
async function main(options: Options) {
  console.log('🚀 Starting ShipStation sync status check...');
  console.log(`Options: Status=${options.status || 'awaiting_shipment'}, Days=${options.days || 'All'}, Dry Run=${options.dryRun || false}`);

  try {
    // Fetch orders from ShipStation
    const shipstationOrders = await fetchShipStationOrders(options);
    
    if (shipstationOrders.size === 0) {
      console.log('\n✅ No orders found in ShipStation with the specified criteria.');
      return;
    }

    // Check database sync status
    const results = await checkDatabaseSync(shipstationOrders);

    // Display results
    displayResults(results, options);

  } catch (error) {
    console.error('\n❌ Script failed:', error);
    throw error;
  }
}

// CLI Setup
const program = new Command();
program
  .version('1.0.0')
  .description('Check sync status between ShipStation orders and local database')
  .option('-d, --days <number>', 'Limit check to orders created in the last N days', parseInt)
  .option('-s, --status <status>', 'Order status to check (default: awaiting_shipment)', 'awaiting_shipment')
  .option('--dry-run', 'Perform checks without making any changes')
  .option('-v, --verbose', 'Enable verbose output')
  .parse(process.argv);

const options = program.opts<Options>();

// Run the script
main(options)
  .catch(async (error) => {
    console.error('\n❌ Script failed unexpectedly:', error);
    await prisma.$disconnect();
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
    console.log('\n🔌 Database connection closed.');
  });
